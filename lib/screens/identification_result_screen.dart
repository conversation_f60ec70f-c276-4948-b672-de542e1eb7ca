import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:lucide_flutter/lucide_flutter.dart';
import 'dart:io';
import '../api/openrouter_service.dart';
import '../models/plant.dart';
import '../providers/plant_provider.dart';
import '../theme.dart';
import 'my_plants_screen.dart';

class IdentificationResultScreen extends StatefulWidget {
  final File imageFile;

  const IdentificationResultScreen({
    Key? key,
    required this.imageFile,
  }) : super(key: key);

  @override
  State<IdentificationResultScreen> createState() => _IdentificationResultScreenState();
}

class _IdentificationResultScreenState extends State<IdentificationResultScreen> {
  final OpenRouterService _apiService = OpenRouterService();
  bool _isLoading = true;
  Map<String, dynamic>? _identificationResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _identifyPlant();
  }

  Future<void> _identifyPlant() async {
    try {
      final result = await _apiService.identifyPlant(widget.imageFile);
      if (mounted) {
        setState(() {
          _identificationResult = result;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _addToMyPlants() {
    if (_identificationResult == null) return;

    final plant = Plant(
      id: DateTime.now().millisecondsSinceEpoch,
      name: _identificationResult!['name'] ?? 'Unknown Plant',
      scientificName: _identificationResult!['scientificName'] ?? 'Species unknown',
      imageUrl: widget.imageFile.path,
      status: 'Healthy',
      care: CareRequirements(
        light: _identificationResult!['care']?['light'] ?? 'Bright, indirect sunlight',
        water: _identificationResult!['care']?['water'] ?? 'Water when soil is dry',
        temperature: _identificationResult!['care']?['temperature'] ?? '18-24°C',
      ),
      dateAdded: DateTime.now(),
      category: _identificationResult!['category'] ?? 'Indoor',
      confidenceLevel: _identificationResult!['confidence']?.toDouble() ?? 0.5,
    );

    Provider.of<PlantProvider>(context, listen: false).addPlant(plant);

    // Show success message and navigate
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${plant.name} added to your plants!'),
        backgroundColor: AppTheme.primaryColor,
        behavior: SnackBarBehavior.floating,
      ),
    );

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const MyPlantsScreen()),
    );
  }

  void _shareResult() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon!'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Plant Identification'),
        leading: IconButton(
          icon: const Icon(LucideIcons.arrowLeft),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(LucideIcons.share),
            onPressed: _shareResult,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Plant Image
            Container(
              width: double.infinity,
              height: 300,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                image: DecorationImage(
                  image: FileImage(widget.imageFile),
                  fit: BoxFit.cover,
                ),
              ),
            ),

            const SizedBox(height: 24),

            if (_isLoading) ...[
              // Loading State
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(color: AppTheme.primaryColor),
                    SizedBox(height: 16),
                    Text(
                      'Identifying your plant...',
                      style: TextStyle(
                        color: AppTheme.secondaryTextColor,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ] else if (_error != null) ...[
              // Error State
              Center(
                child: Column(
                  children: [
                    const Icon(
                      LucideIcons.alertCircle,
                      color: AppTheme.errorColor,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to identify plant',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: AppTheme.errorColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _error!,
                      style: const TextStyle(color: AppTheme.secondaryTextColor),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _isLoading = true;
                          _error = null;
                        });
                        _identifyPlant();
                      },
                      child: const Text('Try Again'),
                    ),
                  ],
                ),
              ),
            ] else if (_identificationResult != null) ...[
              // Success State
              Text(
                _identificationResult!['name'] ?? 'Unknown Plant',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 8),

              Text(
                _identificationResult!['scientificName'] ?? 'Species unknown',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: AppTheme.secondaryTextColor,
                ),
              ),

              const SizedBox(height: 16),

              // Confidence Level
              if (_identificationResult!['confidence'] != null) ...[
                Row(
                  children: [
                    const Icon(
                      LucideIcons.target,
                      size: 16,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Confidence: ${(_identificationResult!['confidence'] * 100).toInt()}%',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
              ],

              // Care Requirements
              Text(
                'Care Requirements',
                style: Theme.of(context).textTheme.headlineSmall,
              ),

              const SizedBox(height: 16),

              _CareRequirementCard(
                icon: LucideIcons.sun,
                title: 'Light',
                description: _identificationResult!['care']?['light'] ?? 'Bright, indirect sunlight',
              ),

              const SizedBox(height: 12),

              _CareRequirementCard(
                icon: LucideIcons.droplets,
                title: 'Water',
                description: _identificationResult!['care']?['water'] ?? 'Water when soil is dry',
              ),

              const SizedBox(height: 12),

              _CareRequirementCard(
                icon: LucideIcons.thermometer,
                title: 'Temperature',
                description: _identificationResult!['care']?['temperature'] ?? '18-24°C',
              ),

              const SizedBox(height: 32),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _addToMyPlants,
                      icon: const Icon(LucideIcons.plus, color: Colors.black),
                      label: const Text(
                        'Add to My Plants',
                        style: TextStyle(color: Colors.black),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _shareResult,
                      icon: const Icon(LucideIcons.share, color: AppTheme.primaryColor),
                      label: const Text(
                        'Share Result',
                        style: TextStyle(color: AppTheme.primaryColor),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: AppTheme.primaryColor),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _CareRequirementCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;

  const _CareRequirementCard({
    Key? key,
    required this.icon,
    required this.title,
    required this.description,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
